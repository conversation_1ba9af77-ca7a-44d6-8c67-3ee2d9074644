package org.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.scrm.base.constant.Constants;
import org.scrm.base.constant.HttpStatus;
import org.scrm.base.core.domain.AjaxResult;
import org.scrm.base.core.domain.entity.SysDept;
import org.scrm.base.core.domain.entity.SysUser;
import org.scrm.base.core.domain.model.LoginUser;
import org.scrm.base.core.page.TableDataInfo;
import org.scrm.base.core.page.TableSupport;
import org.scrm.base.enums.SopExecuteStatus;
import org.scrm.base.enums.SopType;
import org.scrm.base.utils.DateUtils;
import org.scrm.base.utils.SecurityUtils;
import org.scrm.base.utils.StringUtils;
import org.scrm.base.utils.WeekDateUtils;
import org.scrm.config.rabbitmq.RabbitMQSettingConfig;
import org.scrm.domain.WeCustomer;
import org.scrm.domain.WeGroup;
import org.scrm.domain.customer.query.WeCustomersQuery;
import org.scrm.domain.customer.vo.WeCustomersVo;
import org.scrm.domain.groupchat.query.WeGroupChatQuery;
import org.scrm.domain.groupchat.vo.LinkGroupChatListVo;
import org.scrm.domain.media.WeMessageTemplate;
import org.scrm.domain.sop.*;
import org.scrm.domain.sop.dto.WeSopBaseDto;
import org.scrm.domain.sop.dto.WeSopPushTimeDto;
import org.scrm.domain.sop.vo.*;
import org.scrm.domain.sop.vo.content.*;
import org.scrm.domain.wecom.query.customer.msg.WeGetGroupMsgListQuery;
import org.scrm.domain.wecom.vo.customer.msg.WeGroupMsgListVo;
import org.scrm.fegin.QwCustomerClient;
import org.scrm.fegin.QwSysDeptClient;
import org.scrm.fegin.QwSysUserClient;
import org.scrm.mapper.WeSopBaseMapper;
import org.scrm.mapper.WeSopPushTimeMapper;
import org.scrm.service.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【we_sop_base(Sop base表)】的数据库操作Service实现
 * @createDate 2022-09-06 10:33:34
 */
@Service
@Slf4j
public class WeSopBaseServiceImpl extends ServiceImpl<WeSopBaseMapper, WeSopBase>
        implements IWeSopBaseService {

    @Autowired
    private IWeSopAttachmentsService iWeSopAttachmentsService;


    @Autowired
    private IWeSopPushTimeService iWeSopPushTimeService;


    @Lazy
    @Autowired
    private IWeSopExecuteTargetAttachmentsService iWeSopExecuteTargetAttachmentsService;


    @Autowired
    private IWeSopExecuteTargetService executeTargetService;


    @Autowired
    private IWeGroupService iWeGroupService;

    @Autowired
    private IWePhoneCallRecordService phoneCallRecordService;


    @Autowired
    private RabbitMQSettingConfig rabbitMQSettingConfig;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Autowired
    private QwCustomerClient qwCustomerClient;


    @Autowired
    private IWeCustomerService iWeCustomerService;


    @Autowired
    private QwSysUserClient qwSysUserClient;


    @Autowired
    private QwSysDeptClient qwSysDeptClient;

    @Autowired
    private IWeSopExecuteTargetService iWeSopExecuteTargetService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWeSop(WeSopBase weSopBase) {
        if (save(weSopBase)) {
            List<WeSopPushTime> weSopPushTimes = weSopBase.getWeSopPushTimes();
            if (CollectionUtil.isNotEmpty(weSopPushTimes)) {
                weSopPushTimes.stream().forEach(weSopPushTime -> {
                    weSopPushTime.setSopBaseId(weSopBase.getId());
                    if (iWeSopPushTimeService.save(weSopPushTime)) {
                        List<WeMessageTemplate> weMessageTemplates = weSopPushTime.getAttachments();
                        if (CollectionUtil.isNotEmpty(weMessageTemplates)) {
                            Integer tip
                                    = iWeSopAttachmentsService
                                    .saveBatchBySopBaseId(weSopPushTime.getId(), weSopBase.getId(), weMessageTemplates);
                        }
                    }
                });

                //发送mq消息生成执行任务
                this.sendCreateSopMessage(weSopBase.getId());
            }

            log.info("SOP {} 创建成功", weSopBase.getId());
        }
    }

    @Override
    public void reevaluateCustomerForSops(String userId, String externalUserid) {
        try {
            // 1. 获取客户信息
            WeCustomer customer = iWeCustomerService.getOne(new LambdaQueryWrapper<WeCustomer>()
                    .eq(WeCustomer::getExternalUserid, externalUserid)
                    .eq(WeCustomer::getAddUserId, userId)
            );

            if (customer == null) {
                log.warn("makeLabel-未找到客户信息，客户ID: {}, 员工ID: {}", externalUserid, userId);
                return;
            }

            // 2. 先检查并移除客户不再符合条件的SOP执行计划
            removeIneligibleSopExecutions(userId, externalUserid, customer);

            // 3. 获取所有活跃的客户SOP
            List<WeSopBase> activeSops = this.list(new LambdaQueryWrapper<WeSopBase>()
                    .eq(WeSopBase::getBaseType, 1) // 客户SOP
                    .eq(WeSopBase::getSopState, 1) // 执行中
                    .orderByDesc(WeSopBase::getCreateTime)
            );

            if (CollectionUtil.isEmpty(activeSops)) {
                log.info("makeLabel-没有找到需要重新评估的SOP，客户ID: {}, 员工ID: {}", externalUserid, userId);
                return;
            }

            // 4. 逐个评估SOP（增加计数器）
            int addedCount = 0;
            for (WeSopBase sopBase : activeSops) {
                log.info("makeLabel-开始评估SOP，客户ID: {}, SOP ID: {}, SOP名称: {}",
                        externalUserid, sopBase.getId(), sopBase.getSopName());

                if (evaluateAndAddCustomerToSop(sopBase, customer)) {
                    addedCount++;
                    log.info("makeLabel-客户成功添加到SOP执行计划，客户ID: {}, SOP ID: {}, SOP名称: {}",
                            externalUserid, sopBase.getId(), sopBase.getSopName());
                } else {
                    log.info("makeLabel-客户未添加到SOP执行计划，客户ID: {}, SOP ID: {}, SOP名称: {}",
                            externalUserid, sopBase.getId(), sopBase.getSopName());
                }
            }

            log.info("makeLabel-SOP客户重新评估完成，客户ID: {}, 员工ID: {}, 检查SOP数: {}, 新增执行计划: {}",
                    externalUserid, userId, activeSops.size(), addedCount);

        } catch (Exception e) {
            log.error("makeLabel-重新评估客户SOP失败，客户ID: {}, 员工ID: {}, 错误: {}",
                    externalUserid, userId, e.getMessage(), e);
        }
    }

    /**
     * 评估客户是否符合SOP条件，如果符合且未在执行计划中则添加
     *
     * @return true-成功添加，false-未添加
     */
    private boolean evaluateAndAddCustomerToSop(WeSopBase sopBase, WeCustomer customer) {
        try {
            // 1. 检查客户是否符合SOP条件
            if (!checkCustomerEligibility(sopBase, customer)) {
                return false;
            }

            // 2. 检查客户是否已在该SOP的进行中执行目标中
            WeSopExecuteTarget existingTarget = iWeSopExecuteTargetService.getOne(new LambdaQueryWrapper<WeSopExecuteTarget>()
                    .eq(WeSopExecuteTarget::getSopBaseId, sopBase.getId())
                    .eq(WeSopExecuteTarget::getTargetId, customer.getExternalUserid())
                    .eq(WeSopExecuteTarget::getTargetType, 1) // 客户类型
                    .eq(WeSopExecuteTarget::getExecuteState, SopExecuteStatus.SOP_STATUS_ING.getType()) // 只检查进行中的
            );

            if (existingTarget != null) {
                log.info("makeLabel-客户 {} 已在SOP {} 的执行计划中，跳过", customer.getExternalUserid(), sopBase.getId());
                return false;
            }

            // 3. 添加客户到SOP执行计划
            addCustomerToSopPlan(sopBase, customer);

            log.info("makeLabel-客户 {} 已添加到SOP {} ({}) 的执行计划中",
                    customer.getExternalUserid(), sopBase.getId(), sopBase.getSopName());
            return true;

        } catch (Exception e) {
            log.error("makeLabel-评估SOP {} 对客户 {} 的适用性失败: {}",
                    sopBase.getId(), customer.getExternalUserid(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查客户是否符合SOP条件
     */
    private boolean checkCustomerEligibility(WeSopBase sopBase, WeCustomer customer) {
        // 1. 检查SOP时间条件
        if (!checkSopTimeCondition(sopBase, customer)) {
            log.info("makeLabel-客户不符合SOP时间条件，客户ID: {}, SOP ID: {}",
                    customer.getExternalUserid(), sopBase.getId());
            return false;
        }

        // 2. 如果SOP没有设置筛选条件，则所有客户都符合
        if (sopBase.getScopeType() == null || sopBase.getScopeType() == 0) {
            return true;
        }

        // 3. 如果设置了按条件筛选
        if (sopBase.getScopeType() == 1 && sopBase.getWeCustomersQuery() != null) {
            // 创建查询条件的副本，避免修改原始配置
            WeCustomersQuery originalQuery = sopBase.getWeCustomersQuery();
            WeCustomersQuery query = new WeCustomersQuery();

            // 复制SOP配置中的筛选条件
            query.setTagIds(originalQuery.getTagIds());
            query.setTagNames(originalQuery.getTagNames());
            query.setTagNumber(originalQuery.getTagNumber());
            query.setIsContain(originalQuery.getIsContain());
            query.setExcludeTagIds(originalQuery.getExcludeTagIds());
            query.setGender(originalQuery.getGender());
            query.setTrackState(originalQuery.getTrackState());
            query.setAddMethod(originalQuery.getAddMethod());
            query.setCustomerType(originalQuery.getCustomerType());

            // 设置特定客户的查询条件
            query.setExternalUserid(customer.getExternalUserid());
            query.setFirstUserId(customer.getAddUserId());
            query.setDelFlag(Constants.COMMON_STATE);

            log.info("makeLabel-检查客户SOP条件，客户ID: {}, SOP ID: {}, 标签条件: {}, 包含关系: {}",
                    customer.getExternalUserid(), sopBase.getId(), query.getTagIds(), query.getIsContain());

            // 查询符合条件的客户
            List<WeCustomersVo> eligibleCustomers = iWeCustomerService.findWeCustomerList(query, null);

            boolean isEligible = CollectionUtil.isNotEmpty(eligibleCustomers);
            log.info("makeLabel-客户SOP条件检查结果，客户ID: {}, SOP ID: {}, 符合条件: {}, 查询结果数量: {}",
                    customer.getExternalUserid(), sopBase.getId(), isEligible,
                    eligibleCustomers != null ? eligibleCustomers.size() : 0);

            return isEligible;
        }

        return false;
    }

    /**
     * 检查SOP时间条件
     *
     * @param sopBase  SOP配置
     * @param customer 客户信息
     * @return true-符合时间条件，false-不符合时间条件
     */
    private boolean checkSopTimeCondition(WeSopBase sopBase, WeCustomer customer) {
        try {
            // 1. 检查SOP执行条件中的时间配置（整体生效时间范围）
            if (!checkSopOverallTimeCondition(sopBase, customer)) {
                return false;
            }

            // 2. 检查SOP推送时间配置（具体的推送时间段）
            if (!checkSopPushTimeCondition(sopBase, customer)) {
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查SOP时间条件失败，客户ID: {}, SOP ID: {}, 错误: {}",
                    customer.getExternalUserid(), sopBase.getId(), e.getMessage(), e);
            // 出现异常时，为了安全起见，认为符合条件
            return true;
        }
    }

    /**
     * 检查SOP整体生效时间条件
     */
    private boolean checkSopOverallTimeCondition(WeSopBase sopBase, WeCustomer customer) {
        WeSopExecuteConditVo executeCustomerOrGroup = sopBase.getExecuteCustomerOrGroup();
        if (executeCustomerOrGroup != null) {
            WeSopExecuteConditVo.WeSopExecuteGroupTimeCondit timeCondit =
                    executeCustomerOrGroup.getWeSopExecuteGroupTimeCondit();

            if (timeCondit != null && timeCondit.isChange()) {
                Date currentTime = new Date();
                Date beginTime = timeCondit.getBeginTime();
                Date endTime = timeCondit.getEndTime();

                // 检查当前时间是否在SOP生效时间范围内
                if (beginTime != null && currentTime.before(beginTime)) {
                    log.info("SOP整体尚未生效，客户ID: {}, SOP ID: {}, 当前时间: {}, SOP开始时间: {}",
                            customer.getExternalUserid(), sopBase.getId(),
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime),
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, beginTime));
                    return false;
                }

                if (endTime != null && currentTime.after(endTime)) {
                    log.info("SOP整体已过期，客户ID: {}, SOP ID: {}, 当前时间: {}, SOP结束时间: {}",
                            customer.getExternalUserid(), sopBase.getId(),
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime),
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
                    return false;
                }

                log.info("SOP整体时间条件检查通过，客户ID: {}, SOP ID: {}",
                        customer.getExternalUserid(), sopBase.getId());
            }
        }
        return true;
    }

    /**
     * 检查SOP推送时间配置
     * 业务逻辑：
     * - 新客SOP与拨打电话SOP：pushTimePre为数字（如"1"、"2"），基于T+0逻辑，不需要校验当前时间
     * - 活动节日SOP：pushTimePre为日期（如"2025-07-02"），基于具体日期，需要校验当前时间是否在活动期间
     */
    private boolean checkSopPushTimeCondition(WeSopBase sopBase, WeCustomer customer) {
        try {
            // 获取SOP的推送时间配置
            List<WeSopPushTime> pushTimes = iWeSopPushTimeService.list(
                    new LambdaQueryWrapper<WeSopPushTime>()
                            .eq(WeSopPushTime::getSopBaseId, sopBase.getId())
                            .eq(WeSopPushTime::getDelFlag, Constants.COMMON_STATE)
            );

            if (CollectionUtil.isEmpty(pushTimes)) {
                log.info("SOP没有配置推送时间，默认允许执行，客户ID: {}, SOP ID: {}",
                        customer.getExternalUserid(), sopBase.getId());
                return true;
            }

            // 判断SOP类型：检查第一个pushTimePre的格式即可（一个SOP不会混合配置）
            String firstPushTimePre = pushTimes.get(0).getPushTimePre();

            if (StringUtils.isNotEmpty(firstPushTimePre) && isNumericString(firstPushTimePre)) {
                // 新客SOP与拨打电话SOP：pushTimePre为数字，基于T+0逻辑，不需要校验当前时间
                log.info("新客SOP与拨打电话SOP，pushTimePre为数字格式，基于T+0逻辑，无需校验当前时间，直接允许执行，客户ID: {}, SOP ID: {}",
                    customer.getExternalUserid(), sopBase.getId());
                return true;
            }

            // 活动节日SOP：需要校验当前时间
            Date currentTime = new Date();
            int validTimeCount = 0;
            int totalTimeCount = pushTimes.size();

            log.info("活动节日SOP，开始检查推送时间配置，客户ID: {}, SOP ID: {}, 总时间段数: {}",
                    customer.getExternalUserid(), sopBase.getId(), totalTimeCount);

            // 检查每个时间段
            for (int i = 0; i < pushTimes.size(); i++) {
                WeSopPushTime pushTime = pushTimes.get(i);
                boolean isValid = checkActivityDateTimeCondition(pushTime, customer, currentTime, i + 1);
                if (isValid) {
                    validTimeCount++;
                    log.info("找到符合条件的时间段，客户ID: {}, SOP ID: {}, 时间段序号: {}/{}",
                            customer.getExternalUserid(), sopBase.getId(), i + 1, totalTimeCount);
                    // 找到一个符合条件的时间段就可以返回true
                    return true;
                }
            }

            log.info("客户不符合任何SOP推送时间条件，客户ID: {}, SOP ID: {}, 检查时间段数: {}, 符合条件数: {}",
                    customer.getExternalUserid(), sopBase.getId(), totalTimeCount, validTimeCount);

            return false;

        } catch (Exception e) {
            log.error("检查SOP推送时间条件失败，客户ID: {}, SOP ID: {}, 错误: {}",
                    customer.getExternalUserid(), sopBase.getId(), e.getMessage(), e);
            return true; // 异常时默认允许
        }
    }

    /**
     * 检查字符串是否为纯数字
     */
    private boolean isNumericString(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查活动日期时间条件（专门用于活动节日SOP）
     */
    private boolean checkActivityDateTimeCondition(WeSopPushTime pushTime, WeCustomer customer, Date currentTime, int timeIndex) {
        try {
            String pushTimePre = pushTime.getPushTimePre(); // 如：2025-07-02
            String pushStartTime = pushTime.getPushStartTime(); // 如：00:15
            String pushEndTime = pushTime.getPushEndTime(); // 如：00:30

            log.info("检查活动时间段 {}: 客户ID: {}, 活动日期: {}, 开始时间: {}, 结束时间: {}",
                    timeIndex, customer.getExternalUserid(), pushTimePre, pushStartTime, pushEndTime);

            if (StringUtils.isEmpty(pushTimePre)) {
                log.warn("活动时间段 {} 日期为空，默认允许，客户ID: {}", timeIndex, customer.getExternalUserid());
                return true;
            }

            // 验证日期格式并解析
            if (!isValidDateFormat(pushTimePre)) {
                log.warn("活动时间段 {} 日期格式无效: {}, 默认允许，客户ID: {}",
                        timeIndex, pushTimePre, customer.getExternalUserid());
                return true;
            }

            try {
                // 处理时间格式
                String formattedStartTime = formatTimeString(pushStartTime);
                String formattedEndTime = formatTimeString(pushEndTime);

                // 构建完整的开始和结束时间
                Date startDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        pushTimePre + " " + formattedStartTime);
                Date endDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        pushTimePre + " " + formattedEndTime);

                // 检查时间范围的合理性
                if (startDateTime.after(endDateTime)) {
                    log.warn("活动时间段 {} 时间范围无效（开始时间晚于结束时间），客户ID: {}, 开始: {}, 结束: {}",
                            timeIndex, customer.getExternalUserid(), pushStartTime, pushEndTime);
                    return true;
                }

                boolean isValid = currentTime.compareTo(startDateTime) >= 0 && currentTime.compareTo(endDateTime) <= 0;

                log.info("活动时间段 {} 检查结果，客户ID: {}, 当前时间: {}, 活动时间: {} ~ {}, 结果: {}",
                        timeIndex, customer.getExternalUserid(),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startDateTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endDateTime),
                        isValid);

                return isValid;

            } catch (Exception dateParseException) {
                log.error("解析活动时间段 {} 失败，客户ID: {}, 日期: {}, 错误: {}",
                        timeIndex, customer.getExternalUserid(), pushTimePre, dateParseException.getMessage());
                return true;
            }

        } catch (Exception e) {
            log.error("检查活动时间段 {} 失败，客户ID: {}, 错误: {}",
                    timeIndex, customer.getExternalUserid(), e.getMessage(), e);
            return true;
        }
    }

    /**
     * 验证日期格式是否有效
     */
    private boolean isValidDateFormat(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }

        // 检查基本格式
        if (!dateStr.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            return false;
        }

        // 尝试解析日期以验证有效性
        try {
            DateUtils.dateTime(DateUtils.YYYY_MM_DD, dateStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 检查特定日期条件（pushTimeType = 1）
     */
    private boolean checkSpecificDateCondition(WeSopPushTime pushTime, Date currentTime, WeCustomer customer, int timeIndex) {
        try {
            String pushTimePre = pushTime.getPushTimePre(); // 如：2024-01-15
            String pushStartTime = pushTime.getPushStartTime(); // 如：09:00:00
            String pushEndTime = pushTime.getPushEndTime(); // 如：18:00:00

            if (StringUtils.isEmpty(pushTimePre)) {
                log.warn("特定日期推送时间前缀为空，客户ID: {}", customer.getExternalUserid());
                return true;
            }

            // 验证日期格式
            if (!pushTimePre.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                log.warn("特定日期格式无效，客户ID: {}, pushTimePre: {}", customer.getExternalUserid(), pushTimePre);
                return true;
            }

            try {
                // 处理时间格式（如：00:15 → 00:15:00）
                String formattedStartTime = formatTimeString(pushStartTime);
                String formattedEndTime = formatTimeString(pushEndTime);

                // 构建完整的开始和结束时间
                Date startDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        pushTimePre + " " + formattedStartTime);
                Date endDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        pushTimePre + " " + formattedEndTime);

                // 检查时间范围的合理性
                if (startDateTime.after(endDateTime)) {
                    log.warn("特定日期时间范围无效（开始时间晚于结束时间），客户ID: {}, 开始: {}, 结束: {}",
                            customer.getExternalUserid(), pushStartTime, pushEndTime);
                    return true;
                }

                boolean isValid = currentTime.compareTo(startDateTime) >= 0 && currentTime.compareTo(endDateTime) <= 0;

                log.info("时间段 {} 特定日期条件检查，客户ID: {}, 当前时间: {}, 推送时间: {} ~ {}, 结果: {}",
                        timeIndex, customer.getExternalUserid(),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startDateTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endDateTime),
                        isValid);

                return isValid;

            } catch (Exception dateParseException) {
                log.error("解析特定日期时间失败，客户ID: {}, pushTimePre: {}, 错误: {}",
                        customer.getExternalUserid(), pushTimePre, dateParseException.getMessage());
                return true;
            }

        } catch (Exception e) {
            log.error("检查特定日期条件失败，客户ID: {}, 错误: {}", customer.getExternalUserid(), e.getMessage(), e);
            return true;
        }
    }

    /**
     * 检查周期推送条件（pushTimeType = 2）
     */
    private boolean checkWeeklyCondition(WeSopPushTime pushTime, Date currentTime, WeCustomer customer, int timeIndex) {
        try {
            String pushTimePre = pushTime.getPushTimePre(); // 如：1-7，对应周一到周日
            String pushStartTime = pushTime.getPushStartTime(); // 如：09:00:00
            String pushEndTime = pushTime.getPushEndTime(); // 如：18:00:00

            if (StringUtils.isEmpty(pushTimePre) || !pushTimePre.matches("^[1-7]$")) {
                log.warn("周期推送时间配置无效，客户ID: {}, pushTimePre: {}", customer.getExternalUserid(), pushTimePre);
                return true;
            }

            // 获取当前是周几（1=周一，7=周日）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentTime);
            int currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            // Java中周日=1，周一=2...周六=7，需要转换为1=周一，7=周日
            int normalizedDayOfWeek = (currentDayOfWeek == 1) ? 7 : currentDayOfWeek - 1;

            int configuredDayOfWeek = Integer.parseInt(pushTimePre);

            // 检查是否是配置的星期几
            if (normalizedDayOfWeek != configuredDayOfWeek) {
                log.info("周期推送日期不匹配，客户ID: {}, 当前周几: {}, 配置周几: {}",
                        customer.getExternalUserid(), normalizedDayOfWeek, configuredDayOfWeek);
                return false;
            }

            // 检查时间范围
            if (StringUtils.isNotEmpty(pushStartTime) && StringUtils.isNotEmpty(pushEndTime)) {
                String currentDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, currentTime);
                Date startDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, currentDateStr + " " + pushStartTime);
                Date endDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, currentDateStr + " " + pushEndTime);

                boolean isInTimeRange = currentTime.compareTo(startDateTime) >= 0 && currentTime.compareTo(endDateTime) <= 0;

                log.info("周期推送时间范围检查，客户ID: {}, 当前时间: {}, 时间范围: {} ~ {}, 结果: {}",
                        customer.getExternalUserid(),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, currentTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startDateTime),
                        DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endDateTime),
                        isInTimeRange);

                return isInTimeRange;
            }

            // 如果没有配置具体时间，只要是对应的星期几就可以
            log.info("周期推送条件检查通过（无时间限制），客户ID: {}, 当前周几: {}, 配置周几: {}",
                    customer.getExternalUserid(), normalizedDayOfWeek, configuredDayOfWeek);
            return true;

        } catch (Exception e) {
            log.error("检查周期推送条件失败，客户ID: {}, 错误: {}", customer.getExternalUserid(), e.getMessage(), e);
            return true;
        }
    }



    /**
     * 计算客户SOP的实际推送日期，考虑时间窗口延迟逻辑（仅用于客户SOP，群SOP不使用此方法）
     *
     * 处理场景：
     * 1. 第1天 00:15-00:30，客户00:10添加标签 → 当天00:15-00:30执行
     * 2. 第1天 00:15-00:30，客户00:15添加标签 → 当天00:15-00:30执行（边界情况）
     * 3. 第1天 00:15-00:30，客户00:30添加标签 → 当天00:15-00:30执行（边界情况）
     * 4. 第1天 00:15-00:30，客户01:15添加标签 → 延迟到下一天00:15-00:30
     * 5. 第1天 00:15-00:15，客户00:16添加标签 → 延迟到下一天（瞬时时间窗口）
     * 6. 第2天及以后的配置不受影响，按正常T+N逻辑执行
     *
     * @param baseTime 基准时间（客户添加时间或标签变更时间）
     * @param pushTimePre 推送时间前缀（如"1"表示第1天）
     * @param pushStartTime 推送开始时间（如"00:15"）
     * @param pushEndTime 推送结束时间（如"00:30"）
     * @param businessType SOP业务类型（7为拨打电话SOP）
     * @return 实际推送日期
     *
     * 注意：
     * 1. 延迟判断基于baseTime，而不是系统当前时间
     * 2. 此方法用于客户SOP（新客SOP、拨打电话SOP、客户转化SOP），群SOP保持原有T+1逻辑
     * 3. 拨打电话SOP不应用时间窗口延迟逻辑，确保所有配置的时间段都生成推送计划
     */
    private Date calculateActualPushDate(Date baseTime, String pushTimePre, String pushStartTime, String pushEndTime, Integer businessType) {
        try {
            // 验证输入参数
            if (baseTime == null || StringUtils.isEmpty(pushTimePre) ||
                StringUtils.isEmpty(pushStartTime) || StringUtils.isEmpty(pushEndTime)) {
                log.warn("计算推送日期参数无效，baseTime: {}, pushTimePre: {}, pushStartTime: {}, pushEndTime: {}",
                        baseTime, pushTimePre, pushStartTime, pushEndTime);
                return baseTime; // 返回基准时间作为默认值
            }

            // 计算相对天数（T+0逻辑）
            int relativeDays = Integer.parseInt(pushTimePre) - 1;
            Date targetDate = DateUtils.daysAgoOrAfter(baseTime, relativeDays);

            // 拨打电话SOP不应用时间窗口延迟逻辑，直接返回计算结果
            if (businessType != null && businessType.equals(7)) {
                log.info("拨打电话SOP不应用时间窗口延迟逻辑，直接使用计算结果，pushTimePre: {}, 推送日期: {}",
                        pushTimePre, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, targetDate));
                return targetDate;
            }

            // 只对第1天（T+0）进行时间窗口检查和延迟处理（仅适用于新客SOP等）
            if (relativeDays == 0) {
                String targetDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, targetDate);
                String formattedStartTime = formatTimeString(pushStartTime);
                String formattedEndTime = formatTimeString(pushEndTime);

                Date startDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        targetDateStr + " " + formattedStartTime);
                Date endDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS,
                        targetDateStr + " " + formattedEndTime);

                // 检查客户添加时间是否已过时间窗口结束时间
                // 注意：这里使用baseTime（客户添加时间）而不是系统当前时间
                if (baseTime.after(endDateTime)) {
                    // 客户添加时间已过时间窗口，延到下一天
                    targetDate = DateUtils.daysAgoOrAfter(baseTime, 1);
                    log.info("第1天推送时间已过，延到下一天，客户添加时间: {}, 原推送时间窗口: {}-{}, 延后推送日期: {}",
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, baseTime),
                            pushStartTime, pushEndTime,
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, targetDate));
                } else {
                    log.info("第1天推送时间未过，按原计划执行，客户添加时间: {}, 推送时间窗口: {}-{}, 推送日期: {}",
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, baseTime),
                            pushStartTime, pushEndTime,
                            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, targetDate));
                }
            }

            return targetDate;

        } catch (NumberFormatException e) {
            log.error("pushTimePre格式错误，baseTime: {}, pushTimePre: {}, 错误: {}",
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, baseTime), pushTimePre, e.getMessage());
            return baseTime;
        } catch (Exception e) {
            log.error("计算实际推送日期失败，基准时间: {}, pushTimePre: {}, 错误: {}",
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, baseTime), pushTimePre, e.getMessage(), e);
            // 异常时返回原始计算结果
            try {
                int relativeDays = Integer.parseInt(pushTimePre) - 1;
                return DateUtils.daysAgoOrAfter(baseTime, relativeDays);
            } catch (Exception fallbackException) {
                return baseTime;
            }
        }
    }

    /**
     * 计算实际推送天数（用于保存到pushDayNumber字段）
     *
     * @param baseTime 基准时间
     * @param pushTimePre 推送时间前缀
     * @param pushStartTime 推送开始时间
     * @param pushEndTime 推送结束时间
     * @param businessType SOP业务类型（7为拨打电话SOP）
     * @return 实际推送天数
     */
    private Integer calculateActualDayNumber(Date baseTime, String pushTimePre, String pushStartTime, String pushEndTime, Integer businessType) {
        try {
            // 对于非数字格式的pushTimePre（如活动节日SOP），返回null
            if (StringUtils.isEmpty(pushTimePre) || !pushTimePre.matches("^[0-9]+$")) {
                return null;
            }

            int configuredDay = Integer.parseInt(pushTimePre);

            // 只有第1天需要检查延迟，其他天数直接返回配置值
            if (configuredDay != 1) {
                return configuredDay;
            }

            // 第1天需要检查是否延迟
            Date actualPushDate = calculateActualPushDate(baseTime, pushTimePre, pushStartTime, pushEndTime);
            Date originalDate = DateUtils.daysAgoOrAfter(baseTime, 0); // T+0

            // 计算实际推送日期相对于基准时间的天数差
            String originalDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, originalDate);
            String actualDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, actualPushDate);

            try {
                int daysDiff = DateUtils.daysBetween(originalDateStr, actualDateStr);
                // 返回实际的推送天数：基准天(1) + 延迟天数
                return 1 + daysDiff;
            } catch (Exception e) {
                log.error("计算天数差失败，originalDate: {}, actualDate: {}, 错误: {}",
                        originalDateStr, actualDateStr, e.getMessage());
                // 如果计算失败，通过日期字符串比较判断
                if (actualDateStr.equals(originalDateStr)) {
                    return 1; // 未延迟
                } else {
                    return 2; // 延迟了，假设延迟1天
                }
            }

        } catch (Exception e) {
            log.error("计算实际推送天数失败，pushTimePre: {}, 错误: {}", pushTimePre, e.getMessage(), e);
            // 异常时返回配置的天数
            try {
                return Integer.parseInt(pushTimePre);
            } catch (NumberFormatException nfe) {
                return null;
            }
        }
    }

    /**
     * 格式化时间字符串
     * 将 HH:MM 格式转换为 HH:MM:SS 格式
     *
     * @param timeStr 时间字符串，如：00:15 或 00:15:00
     * @return 格式化后的时间字符串，如：00:15:00
     */
    private String formatTimeString(String timeStr) {
        if (StringUtils.isEmpty(timeStr)) {
            return "00:00:00";
        }

        // 如果已经是 HH:MM:SS 格式，直接返回
        if (timeStr.matches("^\\d{2}:\\d{2}:\\d{2}$")) {
            return timeStr;
        }

        // 如果是 HH:MM 格式，添加秒数
        if (timeStr.matches("^\\d{2}:\\d{2}$")) {
            return timeStr + ":00";
        }

        // 其他格式，返回默认值
        log.warn("时间格式无效: {}, 使用默认值 00:00:00", timeStr);
        return "00:00:00";
    }

    /**
     * 将客户添加到SOP执行计划
     *
     * @param sopBase SOP配置
     * @param customer 客户信息
     */
    private void addCustomerToSopPlan(WeSopBase sopBase, WeCustomer customer) {
        // 构建客户数据
        WeCustomersVo customerVo = new WeCustomersVo();
        customerVo.setExternalUserid(customer.getExternalUserid());
        customerVo.setFirstUserId(customer.getAddUserId());

        // 对于标签变更触发的SOP重新评估，使用当前时间作为基准时间
        // 而不是客户最初的添加时间，这样可以正确处理时间窗口逻辑
        customerVo.setFirstAddTime(new Date());

        Map<String, List<WeCustomersVo>> executeWeCustomers = new HashMap<>();
        executeWeCustomers.put(customer.getAddUserId(), Arrays.asList(customerVo));

        // 构建执行计划
        // 对于客户标签变更后的重新评估，应该允许所有类型的SOP生成执行计划，包括新客SOP
        this.builderExecuteCustomerSopPlan(sopBase, executeWeCustomers, false, true);
    }

    //最大尝试次数为 3，初始延迟为 1000 毫秒，倍数为 2，最大延迟为 6000 毫秒。
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2, maxDelay = 6000))
    public void sendCreateSopMessage(Long sopBaseId) {
        //发送mq消息生成执行任务
        rabbitTemplate.convertAndSend(rabbitMQSettingConfig.getSopEx(), rabbitMQSettingConfig.getSopRk(), JSONObject.toJSONString(
                WeSopBaseDto.builder()
                        .sopBaseId(sopBaseId)
                        .isCreateOrUpdate(true).loginUser(
                                SecurityUtils.getLoginUser()
                        ).build()
        ));
    }


    @Override
    @Transactional
    public void updateWeSop(WeSopBase weSopBase) {
        if (updateById(weSopBase)) {
            List<WeSopPushTime> weSopPushTimes = weSopBase.getWeSopPushTimes();

            if (CollectionUtil.isEmpty(weSopPushTimes)) { //任何值未传,则证明需要删除所有内容相关
                if (iWeSopPushTimeService.remove(new LambdaQueryWrapper<WeSopPushTime>()
                        .eq(WeSopPushTime::getSopBaseId, weSopBase.getId()))) {
                    iWeSopAttachmentsService.remove(new LambdaQueryWrapper<WeSopAttachments>()
                            .eq(WeSopAttachments::getSopBaseId, weSopBase.getId()));
                }
            } else {
                //删除当前编辑下移除的任务时间以及素材
                List<Long> noRemoveIds
                        = weSopPushTimes.stream().filter(e -> e.getId() != null).map(WeSopPushTime::getId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(noRemoveIds)) {
                    if (iWeSopPushTimeService.remove(new LambdaQueryWrapper<WeSopPushTime>()
                            .eq(WeSopPushTime::getSopBaseId, weSopBase.getId())
                            .notIn(WeSopPushTime::getId, noRemoveIds))) {
                        iWeSopAttachmentsService.remove(new LambdaQueryWrapper<WeSopAttachments>()
                                .eq(WeSopAttachments::getSopBaseId, weSopBase.getId())
                                .notIn(WeSopAttachments::getSopPushTimeId, noRemoveIds));
                    }
                }

                weSopPushTimes.stream().forEach(weSopPushTime -> {
                    //新增或者编辑相关的
                    weSopPushTime.setSopBaseId(weSopBase.getId());
                    if (iWeSopPushTimeService.saveOrUpdate(weSopPushTime)) {
                        List<WeMessageTemplate> weMessageTemplates = weSopPushTime.getAttachments();
                        if (CollectionUtil.isNotEmpty(weMessageTemplates)) {
                            iWeSopAttachmentsService.updateBatchBySopBaseId(weSopPushTime.getId(), weSopBase.getId(), weMessageTemplates);
                        }
                    }

                });


            }


            //发送mq消息生成执行任务
            rabbitTemplate.convertAndSend(rabbitMQSettingConfig.getSopEx(), rabbitMQSettingConfig.getSopRk(), JSONObject.toJSONString(
                    WeSopBaseDto.builder()
                            .sopBaseId(weSopBase.getId())
                            .isCreateOrUpdate(false).loginUser(
                                    SecurityUtils.getLoginUser()
                            ).build()
            ));


        }

    }

    @Override
    public WeSopBase findWeSopBaseBySopBaseId(Long sopBaseId) {
        WeSopBase weSopBase
                = getById(sopBaseId);
        if (null != weSopBase) {
            List<WeSopPushTime> sopPushTimes = iWeSopPushTimeService.list(new LambdaQueryWrapper<WeSopPushTime>()
                    .eq(WeSopPushTime::getSopBaseId, sopBaseId));

            if (CollectionUtil.isNotEmpty(sopPushTimes)) {
                sopPushTimes.stream().forEach(sopPushTime -> {
                    List<WeSopAttachments> weSopAttachments = iWeSopAttachmentsService.list(new LambdaQueryWrapper<WeSopAttachments>()
                            .eq(WeSopAttachments::getSopPushTimeId, sopPushTime.getId()));

                    sopPushTime.setWeSopAttachments(
                            weSopAttachments
                    );
                    sopPushTime.setAttachments(
                            iWeSopAttachmentsService.weSopAttachmentsToTemplate(weSopAttachments)
                    );
                });
            }
            weSopBase.setWeSopPushTimes(sopPushTimes);
        }


        return weSopBase;
    }


    @Override
    @Transactional
    public void removeWeSoPBySopId(List<Long> sopId) {

        removeByIds(sopId);
        iWeSopPushTimeService.remove(new LambdaQueryWrapper<WeSopPushTime>()
                .in(WeSopPushTime::getSopBaseId, sopId));
        iWeSopAttachmentsService.remove(new LambdaQueryWrapper<WeSopAttachments>()
                .in(WeSopAttachments::getSopBaseId, sopId));

    }

    @Override
    public TableDataInfo<List<WeSopListsVo>> findWeSopListsVo(WeSopBase weSopBase) {

        TableDataInfo<List<WeSopListsVo>> tableDataInfo = new TableDataInfo();
        tableDataInfo.setCode(HttpStatus.SUCCESS);

        List<WeSopListsVo> weSopListsVos = new ArrayList<>();

        PageHelper.startPage(TableSupport.buildPageRequest().getPageNum(), TableSupport.buildPageRequest().getPageSize());
        List<WeSopBase> weSopBases = this.baseMapper.findWeSops(new LambdaQueryWrapper<WeSopBase>()
                .like(StringUtils.isNotEmpty(weSopBase.getSopName()), WeSopBase::getSopName, weSopBase.getSopName())
                .eq(weSopBase.getBaseType() != null, WeSopBase::getBaseType, weSopBase.getBaseType())
                .eq(weSopBase.getSopState() != null, WeSopBase::getSopState, weSopBase.getSopState())
                .eq(weSopBase.getBusinessType() != null, WeSopBase::getBusinessType, weSopBase.getBusinessType())
                .ne(weSopBase.getNeBusinessType() != null, WeSopBase::getNeBusinessType, weSopBase.getNeBusinessType())
                .apply(StringUtils.isNotEmpty(weSopBase.getBeginTime()) && StringUtils.isNotEmpty(weSopBase.getEndTime()),
                        "date_format(create_time,'%Y-%m-%d') BETWEEN '" +
                                weSopBase.getBeginTime()
                                + "' AND '" +
                                weSopBase.getEndTime() + "'")
                .eq(WeSopBase::getDelFlag, Constants.COMMON_STATE)
                .orderByDesc(WeSopBase::getCreateTime)
        );

        PageInfo<WeSopBase> pageInfo = new PageInfo<>(weSopBases);

        if (CollectionUtil.isNotEmpty(pageInfo.getList())) {
            pageInfo.getList().stream().forEach(k -> {
                WeSopListsVo weSopListsVo = WeSopListsVo.builder()
                        .businessType(k.getBusinessType())
                        .sopBaseId(k.getId())
                        .sopName(k.getSopName())
                        .createBy(k.getCreateBy())
                        .createTime(k.getCreateTime())
                        .sopState(k.getSopState())
                        .sendType(k.getSendType())
                        .build();


                if (k.getBaseType().equals(new Integer(1))) {//客户sop

                    if (new Integer(0).equals(k.getScopeType())) { //全部
                        weSopListsVo.setExecuteUser("全部");
                    } else if (new Integer(1).equals(k.getScopeType())) {//部分
                        if (k.getWeCustomersQuery() != null) {
                            weSopListsVo.setExecuteUser(k.getWeCustomersQuery().getUserNames());
                        }
                    }

                } else if (k.getBaseType().equals(new Integer(2))) {//客群sop

                    //设置执行成员
                    WeSopExecuteUserConditVo executeWeUser = k.getExecuteWeUser();


                    log.info("客群条件:" + executeWeUser);
                    log.info("json客群条件:" + JSONUtil.toJsonStr(
                            executeWeUser
                    ));

                    if (Objects.isNull(executeWeUser)) {//执行成员为空则查询当前系统所有员工
                        weSopListsVo.setExecuteUser("全部");
                    } else {//查询成员，或者部门岗位
                        StringBuilder sb = new StringBuilder();
                        //设置具体执行员工
                        WeSopExecuteUserConditVo.ExecuteUserCondit executeUserCondit
                                = executeWeUser.getExecuteUserCondit();
                        if (null != executeUserCondit && executeUserCondit.isChange()
                                && CollectionUtil.isNotEmpty(executeUserCondit.getWeUserIds())) {

                            AjaxResult<List<SysUser>> listAjaxResult = qwSysUserClient.findAllSysUser(
                                    Joiner.on(",").join(executeUserCondit.getWeUserIds()), null, null);

                            if (null != listAjaxResult && CollectionUtil.isNotEmpty(listAjaxResult.getData())) {

                                sb.append(
                                        listAjaxResult.getData().stream().map(SysUser::getUserName).collect(Collectors.toSet())
                                                .stream().collect(Collectors.joining(","))
                                );
                            }


                        }

                        //设置执行的部门或岗位
                        WeSopExecuteUserConditVo.ExecuteDeptCondit executeDeptCondit
                                = executeWeUser.getExecuteDeptCondit();
                        if (null != executeDeptCondit && executeDeptCondit.isChange()) {
                            if (CollectionUtil.isNotEmpty(executeDeptCondit.getDeptIds())) {//设置部门
                                AjaxResult<List<SysDept>> result
                                        = qwSysDeptClient.findSysDeptByIds(StringUtils.join(executeDeptCondit.getDeptIds(), ","));

                                if (null != result && CollectionUtil.isNotEmpty(result.getData())) {
                                    sb.append(",").append(result.getData().stream().map(SysDept::getDeptName).collect(Collectors.joining(",")));
                                }


                            }

                            if (CollectionUtil.isNotEmpty(executeDeptCondit.getPosts())) {//设置岗位
                                sb.append(",").append(Joiner.on(",").join(executeDeptCondit.getPosts()));

                            }

                        }

                        weSopListsVo.setExecuteUser(sb.toString());
                    }

                }


                //设置sop数据
                List<WeSopPushTime> weSopPushTimes = iWeSopPushTimeService.list(new LambdaQueryWrapper<WeSopPushTime>()
                        .eq(WeSopPushTime::getSopBaseId, k.getId()));
                if (CollectionUtil.isNotEmpty(weSopPushTimes)) {
                    //推送所需要的次数
                    weSopListsVo.setPushNeedUserNumber(weSopPushTimes.size());
                    //推送所需天数
                    weSopListsVo.setPushNeedDayNumber(weSopPushTimes.stream().collect(Collectors.groupingBy(WeSopPushTime::getPushTimePre)).size());

                }
                weSopListsVos.add(
                        weSopListsVo
                );
            });


        }

        tableDataInfo.setTotal(pageInfo.getTotal());
        tableDataInfo.setRows(weSopListsVos);


        return tableDataInfo;
    }

    @Override
    public WeSopDetailTabVo findWeSopDetailTabVo(String sopBaseId) {
        return this.baseMapper.findWeSopDetailTabVo(sopBaseId);
    }

    @Override
    public List<WeSopDetailGroupVo> findWeSopDetailGroup(String sopBaseId, String groupName, Integer executeState, String weUserId) {
        return this.baseMapper.findWeSopDetailGroup(sopBaseId, groupName, executeState, weUserId);
    }

    @Override
    public List<WeSopDetailCustomerVo> findWeSopDetailCustomer(String sopBaseId, String customerName, Integer executeState, String weUserId) {
        return this.baseMapper.findWeSopDetailCustomer(sopBaseId, customerName, executeState, weUserId);
    }

    @Override
    public List<WeCustomerSopContentVo> findCustomerExecuteContent(String executeWeUserId, String targetId, Integer executeSubState, String sopBaseId, String executeTargetId) {
        List<WeCustomerSopContentVo> weCustomerSopContentVos = new ArrayList<>();

        List<WeCustomerSopBaseContentVo> customerExecuteContent
                = this.baseMapper.findCustomerExecuteContent(null, executeWeUserId, targetId, executeSubState, sopBaseId, executeTargetId, true);
        if (CollectionUtil.isNotEmpty(customerExecuteContent)) {


            customerExecuteContent.stream()
                    .filter(content -> content.getPushStartTime() != null)
                    .collect(Collectors.groupingBy(WeCustomerSopBaseContentVo::getPushStartTime))
                    .forEach((k, v) -> {
                        WeCustomerSopContentVo weCustomerSopContentVo = new WeCustomerSopContentVo();
                        v.stream().forEach(vv -> {
                            BeanUtils.copyProperties(vv, weCustomerSopContentVo);
                            WeSopAttachments weSopAttachments = iWeSopAttachmentsService.getById(
                                    vv.getSopAttachmentId()
                            );
                            if (null != weSopAttachments) {
                                //设置目标与实际推送内容
                                weCustomerSopContentVo.addWeQrAttachments(
                                        weSopAttachments, vv.getExecuteState()
                                );
                            }
                        });


                        //设置已发送天数
                        if (v.stream().filter(v1 -> v1.getExecuteState() == 1).collect(Collectors.counting()) > 0) {
                            weCustomerSopContentVo.setSendDayNumber(1);
                        }


                        //设置已发送次数
                        weCustomerSopContentVo.setSendNumber(
                                v.stream().filter(v1 -> v1.getExecuteState() == 1).collect(Collectors.counting()).intValue()
                        );


                        weCustomerSopContentVos.add(weCustomerSopContentVo);
                    });


        }


        if (CollectionUtil.isNotEmpty(weCustomerSopContentVos)) {
            return weCustomerSopContentVos.stream().sorted(Comparator.comparing(WeCustomerSopContentVo::getPushStartTime)).collect(Collectors.toList());
        }

        return weCustomerSopContentVos;
    }


    @Override
    public WeSendCustomerSopContentVo findCustomerSopContent(String executeWeUserId, String targetId, Integer executeSubState) {
        WeSendCustomerSopContentVo weSendCustomerSopContentVo = new WeSendCustomerSopContentVo();
        List<WeCustomer> weCustomerList = iWeCustomerService.list(new LambdaQueryWrapper<WeCustomer>()
                .eq(WeCustomer::getExternalUserid, targetId)
                .eq(WeCustomer::getAddUserId, executeWeUserId));

        //设置客户相关信息
        if (CollectionUtil.isNotEmpty(weCustomerList)) {
            BeanUtils.copyProperties(weCustomerList.stream().findFirst().get(), weSendCustomerSopContentVo);
        }


        List<WeCustomerSopBaseContentVo> customerExecuteContent
                = this.baseMapper.findCustomerExecuteContent(1, executeWeUserId, targetId, executeSubState, null, null, true);

        if (CollectionUtil.isNotEmpty(customerExecuteContent)) {
            List<WeSendCustomerSopContentVo.WeCustomerSop> weCustomerSops = new ArrayList<>();

            customerExecuteContent.stream()
                    .collect(Collectors.groupingBy(WeCustomerSopBaseContentVo::getSopBaseId)).forEach((k, v) -> {

                        WeCustomerSopBaseContentVo firstContent = v.stream().findFirst().get();
                        WeSendCustomerSopContentVo.WeCustomerSop weCustomerSop = WeSendCustomerSopContentVo.WeCustomerSop.builder()
                                .sopBaseId(firstContent.getSopBaseId())
                                .sopName(firstContent.getSopName())
                                .businessType(firstContent.getBusinessType())
                                .build();


                        List<WeSendCustomerSopContentVo.WeCustomerSopContent> weCustomerSopContents = new ArrayList<>();
                        v.stream().forEach(vv -> {

                            weCustomerSopContents.add(
                                    WeSendCustomerSopContentVo.WeCustomerSopContent.builder()
                                            .executeState(vv.getExecuteState())
                                            .pushEndTime(vv.getPushEndTime())
                                            .pushStartTime(vv.getPushStartTime())
                                            .weQrAttachments(
                                                    iWeSopAttachmentsService.getById(
                                                            vv.getSopAttachmentId()
                                                    )
                                            )
                                            .executeTargetAttachId(vv.getExecuteTargetAttachId())
                                            .build()
                            );

                        });
                        weCustomerSop.setWeCustomerSopContents(weCustomerSopContents);

                        weCustomerSops.add(weCustomerSop);
                    });


            weSendCustomerSopContentVo.setWeCustomerSops(weCustomerSops);
        }


        return weSendCustomerSopContentVo;

    }




    @Override
    public WeSendCustomerSopContentVo findPhoneCallSopContentBySopBaseId(String executeWeUserId, Integer executeSubState, String sopBaseId, String executeTargetAttachId) {
        log.info("[PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: {}, SOP基础ID: {}, 执行目标附件ID: {}", executeWeUserId, sopBaseId, executeTargetAttachId);

        // 参数验证
        if (StringUtils.isEmpty(executeWeUserId)) {
            log.warn("[PHONE_CALL_SOP_DEBUG] 员工ID为空，返回空结果");
            return createEmptyPhoneCallSopResult();
        }

        if (StringUtils.isEmpty(sopBaseId)) {
            log.warn("[PHONE_CALL_SOP_DEBUG] SOP基础ID为空，返回空结果");
            return createEmptyPhoneCallSopResult();
        }

        WeSendCustomerSopContentVo weSendCustomerSopContentVo = createEmptyPhoneCallSopResult();

        // 查询该员工的特定拨打电话SOP任务
        log.info("[PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: {}, 业务类型: {}, SOP基础ID: {}", executeWeUserId, 7, sopBaseId);

        // 查询SOP基础信息
        try {
            WeSopBase sopBase = this.getById(sopBaseId);
            if (sopBase != null) {
                log.info("[PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: {}, 名称: '{}', 业务类型: {}, 状态: {}",
                        sopBase.getId(), sopBase.getSopName(), sopBase.getBusinessType(), sopBase.getSopState());
            } else {
                log.warn("[PHONE_CALL_SOP_DEBUG] 未找到SOP基础信息，sopBaseId: {}", sopBaseId);
            }
        } catch (Exception e) {
            log.error("[PHONE_CALL_SOP_DEBUG] 查询SOP基础信息失败，sopBaseId: {}", sopBaseId, e);
        }

        // 拨打电话SOP不应该按SOP执行状态过滤，因为executeSubState是基于callStatus的
        // 应该查询所有数据，然后在前端根据callStatus进行分组显示
        List<WeCustomerSopBaseContentVo> phoneCallSopContent = this.baseMapper.findPhoneCallSopExecuteContentBySopBaseId(
                executeWeUserId,
                null, // 不限制SOP执行状态，因为executeSubState与execute_state是不同概念
                7, // businessType: 拨打电话SOP
                sopBaseId,
                executeTargetAttachId
        );

        log.info("[PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: {}", phoneCallSopContent != null ? phoneCallSopContent.size() : 0);

        if (phoneCallSopContent != null && !phoneCallSopContent.isEmpty()) {
            List<WeSendCustomerSopContentVo.WeCustomerSop> weCustomerSops = new ArrayList<>();

            // 添加详细的调试日志
            log.info("[PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: {}", phoneCallSopContent.size());
            phoneCallSopContent.forEach(content -> {
                log.info("[PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '{}', 外部ID: '{}', 电话: '{}', SOP ID: {}, SOP名称: '{}', 执行状态: {}, 附件ID: {}",
                        content.getCustomerName(), content.getExternalUserid(), content.getPhone(),
                        content.getSopBaseId(), content.getSopName(), content.getExecuteState(),
                        content.getExecuteTargetAttachId());
            });

            // 按客户分组处理（确保每个客户只有一条记录）
            Map<String, List<WeCustomerSopBaseContentVo>> customerGroupMap = phoneCallSopContent.stream()
                    .filter(content -> StringUtils.isNotEmpty(content.getExternalUserid())) // 过滤掉无效的客户ID
                    .collect(Collectors.groupingBy(WeCustomerSopBaseContentVo::getExternalUserid));

            log.info("[PHONE_CALL_SOP_DEBUG] 分组后的客户数量: {}", customerGroupMap.size());
            customerGroupMap.forEach((externalUserid, list) -> {
                WeCustomerSopBaseContentVo firstContent = list.get(0);
                log.info("[PHONE_CALL_SOP_DEBUG] 客户 '{}' (姓名: '{}', 电话: '{}') 包含 {} 条记录",
                        externalUserid, firstContent.getCustomerName(), firstContent.getPhone(), list.size());
            });

            // 如果用户期望只有一个客户，但实际有多个，记录警告
            if (customerGroupMap.size() > 1) {
                log.warn("[PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: {}) 匹配了 {} 个客户，如果期望只有一个客户，请检查SOP配置",
                        sopBaseId, customerGroupMap.size());

                // 列出所有匹配的客户，便于用户检查
                customerGroupMap.forEach((externalUserid, list) -> {
                    WeCustomerSopBaseContentVo firstContent = list.get(0);
                    log.warn("[PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='{}', 姓名='{}', 电话='{}'",
                            externalUserid, firstContent.getCustomerName(), firstContent.getPhone());
                });
            }

            // 简化：直接使用SQL查询结果中的拨打状态，无需额外查询
            log.info("[PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态");

            customerGroupMap.forEach((externalUserid, contentList) -> {
                        WeCustomerSopBaseContentVo firstContent = contentList.get(0);

                        log.info("[PHONE_CALL_SOP_DEBUG] 处理客户: {}, 记录数: {}", firstContent.getCustomerName(), contentList.size());

                        WeSendCustomerSopContentVo.WeCustomerSop weCustomerSopVo = new WeSendCustomerSopContentVo.WeCustomerSop();
                        weCustomerSopVo.setCustomerName(firstContent.getCustomerName());
                        weCustomerSopVo.setCustomerPhone(firstContent.getPhone());
                        weCustomerSopVo.setExternalUserid(firstContent.getExternalUserid());
                        weCustomerSopVo.setSopName(firstContent.getSopName());
                        weCustomerSopVo.setBusinessType(firstContent.getBusinessType());
                        weCustomerSopVo.setSopBaseId(firstContent.getSopBaseId());

                        // 修复：不再在客户级别设置统一的callStatus
                        // 每个时间段都有独立的callStatus，在WeCustomerSopContent中设置
                        // 为了向后兼容，这里设置为0（客户级别的callStatus将被废弃）
                        weCustomerSopVo.setCallStatus(0);

                        log.info("[PHONE_CALL_SOP_DEBUG] 客户信息: 客户名={}, externalUserid={}, sopBaseId={}, 时间段数量={}",
                                firstContent.getCustomerName(), firstContent.getExternalUserid(), firstContent.getSopBaseId(), contentList.size());

                        // 构建SOP内容列表 - 每个时间段都有独立的callStatus
                        List<WeSendCustomerSopContentVo.WeCustomerSopContent> sopContentList = new ArrayList<>();
                        contentList.forEach(content -> {
                            try {
                                // 获取附件信息，处理可能的空值
                                WeSopAttachments attachment = null;
                                if (StringUtils.isNotEmpty(content.getSopAttachmentId())) {
                                    attachment = iWeSopAttachmentsService.getById(content.getSopAttachmentId());
                                }

                                // 关键修复：每个时间段的每个客户都有独立的callStatus
                                Integer timeSlotCallStatus = content.getCallStatus() != null ? content.getCallStatus() : 0;

                                WeSendCustomerSopContentVo.WeCustomerSopContent contentVo = WeSendCustomerSopContentVo.WeCustomerSopContent.builder()
                                        .executeTargetAttachId(content.getExecuteTargetAttachId())
                                        .pushStartTime(content.getPushStartTime())
                                        .pushEndTime(content.getPushEndTime())
                                        .executeState(content.getExecuteState())
                                        .weQrAttachments(attachment)
                                        // 为每个时间段设置独立的callStatus
                                        .callStatus(timeSlotCallStatus)
                                        .build();

                                sopContentList.add(contentVo);

                                log.info("[PHONE_CALL_SOP_DEBUG] 客户 '{}' 时间段 {} 的拨打状态: executeTargetAttachId={}, callStatus={} ({})",
                                    content.getCustomerName(),
                                    content.getPushStartTime(),
                                    content.getExecuteTargetAttachId(),
                                    timeSlotCallStatus,
                                    timeSlotCallStatus == 1 ? "已拨打" : "待拨打");
                            } catch (Exception e) {
                                log.error("[PHONE_CALL_SOP_DEBUG] 处理SOP内容时出错，跳过该条记录: {}", content.getExecuteTargetAttachId(), e);
                            }
                        });

                        // 按推送时间排序，处理null值
                        sopContentList.sort(Comparator.comparing(
                            WeSendCustomerSopContentVo.WeCustomerSopContent::getPushStartTime,
                            Comparator.nullsLast(Comparator.naturalOrder())
                        ));

                        weCustomerSopVo.setWeCustomerSopContents(sopContentList);
                        weCustomerSops.add(weCustomerSopVo);

                        log.info("[PHONE_CALL_SOP_DEBUG] 处理客户: {}, 电话: {}, SOP内容数量: {}",
                            firstContent.getCustomerName(),
                            firstContent.getPhone(),
                            sopContentList.size());
                    });

            weSendCustomerSopContentVo.setWeCustomerSops(weCustomerSops);
            log.info("[PHONE_CALL_SOP_DEBUG] 构建完成，返回{}个客户的特定拨打电话SOP任务", weCustomerSops.size());

            // 详细记录返回的客户信息
            weCustomerSops.forEach(customerSop -> {
                log.info("[PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='{}', 电话='{}', 外部ID='{}', SOP名称='{}', 拨打状态={}",
                        customerSop.getCustomerName(), customerSop.getCustomerPhone(), customerSop.getExternalUserid(),
                        customerSop.getSopName(), customerSop.getCallStatus());
            });
        } else {
            weSendCustomerSopContentVo.setWeCustomerSops(new ArrayList<>());
            log.info("[PHONE_CALL_SOP_DEBUG] 没有找到特定的拨打电话SOP任务");
        }

        return weSendCustomerSopContentVo;
    }

    /**
     * 创建空的拨打电话SOP结果
     * @return 空的WeSendCustomerSopContentVo对象
     */
    private WeSendCustomerSopContentVo createEmptyPhoneCallSopResult() {
        WeSendCustomerSopContentVo emptyResult = new WeSendCustomerSopContentVo();
        emptyResult.setCustomerName("拨打电话SOP");
        emptyResult.setExternalUserid("phone_call_sop");
        emptyResult.setWeCustomerSops(new ArrayList<>());
        return emptyResult;
    }

    @Override
    public List<WeGroupSopContentVo> findGroupExecuteContent(String chatId, Integer executeState, String sopBaseId, String executeTargetId) {
        List<WeGroupSopContentVo> weGroupSopContentVos = new ArrayList<>();

        List<WeGroupSopBaseContentVo> groupExecuteContent
                = this.baseMapper.findGroupExecuteContent(null, chatId, executeState, sopBaseId, executeTargetId, true, null);
        if (CollectionUtil.isNotEmpty(groupExecuteContent)) {
            groupExecuteContent.stream().collect(Collectors.groupingBy(WeGroupSopBaseContentVo::getPushTimePre))
                    .forEach((k, v) -> {
                        WeGroupSopContentVo weGroupSopContentVo = new WeGroupSopContentVo();

                        v.stream().forEach(vv -> {
                            BeanUtils.copyProperties(vv, weGroupSopContentVo);
                            WeSopAttachments weSopAttachments = iWeSopAttachmentsService.getById(
                                    vv.getSopAttachmentId()
                            );
                            if (null != weSopAttachments) {
                                //设置目标与实际推送内容
                                weGroupSopContentVo.addWeQrAttachments(
                                        weSopAttachments, vv.getExecuteState()
                                );
                            }
                        });


                        //设置已发送天数
                        if (v.stream().filter(v1 -> v1.getExecuteState() == 1).collect(Collectors.counting()) > 0) {
                            weGroupSopContentVo.setSendDayNumber(1);
                        }


                        //设置已发送次数
                        weGroupSopContentVo.setSendNumber(
                                v.stream().filter(v1 -> v1.getExecuteState() == 1).collect(Collectors.counting()).intValue()
                        );

                        weGroupSopContentVos.add(weGroupSopContentVo);

                    });
        }

        if (CollectionUtil.isNotEmpty(weGroupSopContentVos)) {
            return weGroupSopContentVos.stream().sorted(Comparator.comparing(WeGroupSopContentVo::getPushStartTime)).collect(Collectors.toList());
        }

        return weGroupSopContentVos;
    }


    @Override
    public WeSendGroupSopContentVo findGroupSopContent(Integer sopState, String chatId, Integer executeSubState) {
        WeSendGroupSopContentVo weSendGroupSopContentVo = new WeSendGroupSopContentVo();

        List<WeGroup> weGroups = iWeGroupService.list(new LambdaQueryWrapper<WeGroup>()
                .eq(WeGroup::getChatId, chatId));
        if (CollectionUtil.isNotEmpty(weGroups)) {
            BeanUtils.copyProperties(weGroups.stream().findFirst().get(), weSendGroupSopContentVo);
        }

        List<WeGroupSopBaseContentVo> groupExecuteContent
                = this.baseMapper.findGroupExecuteContent(sopState, chatId, executeSubState, null, null, true, SecurityUtils.getLoginUser().getSysUser().getWeUserId());

        if (CollectionUtil.isNotEmpty(groupExecuteContent)) {
            List<WeSendGroupSopContentVo.WeGroupSop> weGroupSops = new ArrayList<>();


            groupExecuteContent.stream().collect(Collectors.groupingBy(WeGroupSopBaseContentVo::getSopBaseId))
                    .forEach((k, v) -> {

                        WeSendGroupSopContentVo.WeGroupSop weGroupSop = WeSendGroupSopContentVo.WeGroupSop.builder()
                                .sopBaseId(v.stream().findFirst().get().getSopBaseId())
                                .sopName(v.stream().findFirst().get().getSopName())
                                .build();

                        List<WeSendGroupSopContentVo.WeGroupSopContent> weGroupSopContents = new ArrayList<>();


                        v.stream().forEach(vv -> {

                            weGroupSopContents.add(
                                    WeSendGroupSopContentVo.WeGroupSopContent.builder()
                                            .executeTargetAttachId(vv.getExecuteTargetAttachId())
                                            .executeState(vv.getExecuteState())
                                            .pushEndTime(vv.getPushEndTime())
                                            .pushStartTime(vv.getPushStartTime())
                                            .weQrAttachments(
                                                    iWeSopAttachmentsService.getById(
                                                            vv.getSopAttachmentId()
                                                    )
                                            )
                                            .build()
                            );

                        });

                        weGroupSop.setWeGroupSopContents(weGroupSopContents);

                        weGroupSops.add(weGroupSop);

                    });

            weSendGroupSopContentVo.setWeGroupSops(weGroupSops);
        }


        return weSendGroupSopContentVo;

    }


    @Override
    public List<WeCustomerSopToBeSentVo> findWeCustomerSopToBeSent(boolean isExpiringSoon) {
        List<WeCustomerSopToBeSentVo> sopToBeSentVoList = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();

        if (null != loginUser && null != loginUser.getSysUser()) {


            List<WeCustomerSopToBeSentVo> tdSendSopCustomers
                    = this.baseMapper.findTdSendSopCustomers(loginUser.getSysUser().getWeUserId(), isExpiringSoon);

            if (CollectionUtil.isNotEmpty(tdSendSopCustomers)) {


                tdSendSopCustomers.stream()
                        .collect(Collectors.groupingBy(WeCustomerSopToBeSentVo::getExternalUserid)).forEach((k, v) -> {
                            WeCustomerSopToBeSentVo weCustomerSopToBeSentVo = new WeCustomerSopToBeSentVo();
                            BeanUtils.copyProperties(v.stream().findFirst().get(), weCustomerSopToBeSentVo);


                            weCustomerSopToBeSentVo.setWeSopToBeSentContentInfoVo(
                                    this.baseMapper.findSopToBeSentContentInfo(loginUser.getSysUser().getWeUserId(), k)
                            );

                            sopToBeSentVoList.add(weCustomerSopToBeSentVo);
                        });

            }


        }

        return sopToBeSentVoList;
    }

    @Override
    public List<WeGroupSopToBeSentVo> findWeGroupSopToBeSent(boolean isExpiringSoon) {
        List<WeGroupSopToBeSentVo> weGroupSopToBeSentVos = new ArrayList<>();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (null != loginUser && null != loginUser.getSysUser()) {


            List<WeGroupSopToBeSentVo> tdSendSopGroups
                    = this.baseMapper.findTdSendSopGroups(loginUser.getSysUser().getWeUserId(), isExpiringSoon);

            if (CollectionUtil.isNotEmpty(tdSendSopGroups)) {
                tdSendSopGroups.stream()
                        .collect(Collectors.groupingBy(WeGroupSopToBeSentVo::getChatId)).forEach((k, v) -> {
                            WeGroupSopToBeSentVo weGroupSopToBeSentVo = new WeGroupSopToBeSentVo();
                            BeanUtils.copyProperties(v.stream().findFirst().get(), weGroupSopToBeSentVo);

                            weGroupSopToBeSentVo.setWeSopToBeSentContentInfoVo(
                                    this.baseMapper.findSopToBeSentContentInfo(loginUser.getSysUser().getWeUserId(), k)
                            );

                            weGroupSopToBeSentVos.add(weGroupSopToBeSentVo);
                        });
            }


        }
        return weGroupSopToBeSentVos;
    }


    @Override
    public void executeSop(String executeTargetAttachId) {

        WeSopExecuteTargetAttachments targetAttachments
                = iWeSopExecuteTargetAttachmentsService.getById(executeTargetAttachId);
        if (null != targetAttachments) {
            targetAttachments.setExecuteTime(new Date());
            targetAttachments.setExecuteState(1);
            targetAttachments.setIsPushOnTime(
                    DateUtils.isEffectiveDate(new Date(), targetAttachments.getPushStartTime(), targetAttachments.getPushEndTime()) ? 0 : 1
            );

            iWeSopExecuteTargetAttachmentsService.updateById(
                    targetAttachments
            );

            //检索当前sop下当前客户有没有需要检索的
            if (iWeSopExecuteTargetAttachmentsService.count(new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                    .eq(WeSopExecuteTargetAttachments::getExecuteTargetId, targetAttachments.getExecuteTargetId())
                    .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
            ) == 0) { //结束任务同时执行相关sop结束动作

                executeTargetService.sopExecuteEndAction(
                        targetAttachments.getExecuteTargetId()
                );


            }


        }

    }

    @Override
    public Map<String, List<LinkGroupChatListVo>> builderExecuteGroup(WeSopBase weSopBase, String chatId) {

        Map<String, List<LinkGroupChatListVo>> weGroupMap = new HashMap<>();


        //新客群SOP创建的时候直接过滤相关（针对创建的情况）
        if (StringUtils.isEmpty(chatId) && new Integer(4).equals(weSopBase.getBusinessType())) {
            return weGroupMap;
        }

        WeSopExecuteUserConditVo executeWeUser = weSopBase.getExecuteWeUser();
        WeGroupChatQuery weGroupChatQuery = new WeGroupChatQuery();

        weGroupChatQuery.setChatId(chatId);
        if (Objects.nonNull(executeWeUser)) {//非部群
            Set<String> executeWeUserIds = this.builderExecuteWeUserIds(weSopBase.getExecuteWeUser());
            if (CollectionUtil.isNotEmpty(executeWeUserIds)) {
                //设置条件检索出的群主
                weGroupChatQuery.setUserIds(
                        StringUtils.join(executeWeUserIds, ",")
                );
            } else {//部分条件下不存在或者未检索出成员则直接返回，不做sop任务
                return weGroupMap;
            }
        }

        WeSopExecuteConditVo executeCustomerOrGroup = weSopBase.getExecuteCustomerOrGroup();
        //设置生效条件
        if (Objects.nonNull(executeCustomerOrGroup)) {

            //时间
            WeSopExecuteConditVo.WeSopExecuteGroupTimeCondit weSopExecuteGroupTimeCondit
                    = executeCustomerOrGroup.getWeSopExecuteGroupTimeCondit();
            if (Objects.nonNull(weSopExecuteGroupTimeCondit) && weSopExecuteGroupTimeCondit.isChange()) {
                weGroupChatQuery.setBeginTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, weSopExecuteGroupTimeCondit.getBeginTime()));
                weGroupChatQuery.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, weSopExecuteGroupTimeCondit.getEndTime()));
            }

            //标签
            WeSopExecuteConditVo.WeSopExecuteGroupTagIdsCondit weSopExecuteGroupTagIdsCondit
                    = executeCustomerOrGroup.getWeSopExecuteGroupTagIdsCondit();
            if (Objects.nonNull(weSopExecuteGroupTagIdsCondit) && weSopExecuteGroupTagIdsCondit.isChange()) {
                weGroupChatQuery.setTagIds(StringUtils.join(weSopExecuteGroupTagIdsCondit.getTagIds(), ","));
            }

            //客群人数上限范围
            WeSopExecuteConditVo.WeSopExecuteGroupMemberLimitCondit weSopExecuteGroupMemberLimitCondit
                    = executeCustomerOrGroup.getWeSopExecuteGroupMemberLimitCondit();
            if (Objects.nonNull(weSopExecuteGroupMemberLimitCondit) && weSopExecuteGroupMemberLimitCondit.isChange()) {
                weGroupChatQuery.setGroupMemberUp(weSopExecuteGroupMemberLimitCondit.getGroupMemberUp());
                weGroupChatQuery.setGroupMemberDown(weSopExecuteGroupMemberLimitCondit.getGroupMemberDown());
            }
        }


        List<LinkGroupChatListVo> chatListVos
                = iWeGroupService.getPageLimitList(weGroupChatQuery);


        log.info("执行的客群" + chatListVos);

        if (CollectionUtil.isNotEmpty(chatListVos)) {
            weGroupMap.putAll(
                    chatListVos.stream().distinct().collect(Collectors.groupingBy(LinkGroupChatListVo::getOwner))
            );
        }


        return weGroupMap;
    }


    @Override
    public Set<String> builderExecuteWeUserIds(WeSopExecuteUserConditVo executeWeUser) {
        //生成执行任务
        Set<String> executeWeUserIds = new HashSet<>();

//        //1.查询执行成员
//        if(Objects.isNull(executeWeUser)){//全部成员,业务限制
//
//
//            List<LinkGroupChatListVo> pageList
//                    = iWeGroupService.getPageList(new WeGroupChatQuery(), PageDomain.builder()
//                            .pageNum(1)
//                            .pageSize(1000)
//                    .build());
//
//            if(CollectionUtil.isNotEmpty(pageList)){
//                executeWeUserIds.addAll(
//                        pageList.stream().map(LinkGroupChatListVo::getOwner).collect(Collectors.toSet())
//                );
//
//
//            }
//
//        }else{ //部分成员
        //用户选择的成员
        WeSopExecuteUserConditVo.ExecuteUserCondit executeUserCondit = executeWeUser.getExecuteUserCondit();
        if (null != executeUserCondit && executeUserCondit.isChange()) {
            executeWeUserIds.addAll(
                    executeUserCondit.getWeUserIds()
            );
        }

        //按照部门与岗位筛选
        WeSopExecuteUserConditVo.ExecuteDeptCondit executeDeptCondit = executeWeUser.getExecuteDeptCondit();
        if (null != executeDeptCondit && executeDeptCondit.isChange()) {
            String positions = null, depteIds = null;
            if (CollectionUtil.isNotEmpty(executeDeptCondit.getDeptIds())) {
                depteIds = StringUtils.join(executeDeptCondit.getDeptIds(), ",");
            }

            if (CollectionUtil.isNotEmpty(executeDeptCondit.getPosts())) {
                positions = StringUtils.join(executeDeptCondit.getPosts(), ",");
            }


            AjaxResult<List<SysUser>> listAjaxResult = qwSysUserClient.findAllSysUser("", positions, depteIds);

            if (null != listAjaxResult && CollectionUtil.isNotEmpty(listAjaxResult.getData())) {
                executeWeUserIds.addAll(
                        listAjaxResult.getData().stream().map(SysUser::getWeUserId).collect(Collectors.toList())
                );
            }

        }

//        }

        return executeWeUserIds;

    }

    @Override
    public void builderExecuteCustomerSopPlan(WeSopBase weSopBase, Map<String, List<WeCustomersVo>> executeWeCustomers, boolean isCreateOrUpdate1, boolean buildXkSopPlan) {
        if (CollectionUtil.isNotEmpty(executeWeCustomers)) {
            List<WeSopExecuteTarget> weSopExecuteTargets = new ArrayList<>();
            executeWeCustomers.forEach((k, v) -> {
                v.stream().forEach(weCustomer -> {
                    weSopExecuteTargets.add(
                            WeSopExecuteTarget.builder()
                                    .targetId(weCustomer.getExternalUserid())
                                    .targetType(1)
                                    .addCustomerOrCreateGoupTime(weCustomer.getFirstAddTime())
                                    .sopBaseId(weSopBase.getId())
                                    .executeWeUserId(k)
                                    .build()
                    );
                });
            });

            if (CollectionUtil.isNotEmpty(weSopExecuteTargets)) {

                //处理不满足当前条件的生效客户(针对客户sop编辑)
//                if(!isCreateOrUpdate){
//                    executeTargetService.editSopExceptionEnd(weSopBase.getId(),
//                            weSopExecuteTargets.stream().map(WeSopExecuteTarget::getTargetId).collect(Collectors.toList()));
//                }


                if (executeTargetService.saveOrUpdateBatch(weSopExecuteTargets)) {
                    log.info("makeLabel-weSopExecuteTargets",JSONObject.toJSONString(weSopExecuteTargets));

                    List<WeSopPushTimeDto> weSopPushTimeDtos
                            = ((WeSopPushTimeMapper) iWeSopPushTimeService.getBaseMapper()).findWeSopPushTimeDto(weSopBase.getId());
                    if (CollectionUtil.isNotEmpty(weSopPushTimeDtos)) {
                        List<WeSopExecuteTargetAttachments> targetAttachments = new ArrayList<>();

                        weSopExecuteTargets.stream().forEach(weSopExecuteTarget -> {


                            weSopPushTimeDtos.stream().forEach(weSopPushTimeDto -> {

                                WeSopExecuteTargetAttachments attachments = WeSopExecuteTargetAttachments.builder()
                                        .executeTargetId(weSopExecuteTarget.getId())
                                        .sopAttachmentId(weSopPushTimeDto.getSopAttachmentId())
                                        .pushTimePre(weSopPushTimeDto.getPushTimePre())
                                        .pushTimeType(weSopPushTimeDto.getPushTimeType())
                                        .build();
                                if (weSopPushTimeDto.getPushTimeType() == 3) {//设置推送时间（相对推送时间）员工添加客户的时间启始时间

                                    if (weSopPushTimeDto.getPushTimePre().matches("^[0-9]*$")) {

                                        // 确定计算基准时间：
                                        // 1. 拨打电话SOP：使用当前时间（SOP创建时间），确保所有时间段都能生成推送计划
                                        // 2. 新客SOP：使用客户添加时间，保持原有逻辑
                                        Date baseTime;
                                        if (weSopBase.getBusinessType() != null && weSopBase.getBusinessType().equals(7)) {
                                            // 拨打电话SOP：使用当前时间作为基准，避免历史客户的添加时间影响推送计划生成
                                            baseTime = new Date();
                                        } else {
                                            // 新客SOP等：使用客户添加时间
                                            baseTime = weSopExecuteTarget.getAddCustomerOrCreateGoupTime();
                                        }

                                        //推送日期计算
                                        Date actualPushDate = calculateActualPushDate(baseTime,
                                                weSopPushTimeDto.getPushTimePre(),
                                                weSopPushTimeDto.getPushStartTime(),
                                                weSopPushTimeDto.getPushEndTime());
                                        String pushDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, actualPushDate);

                                        // 设置实际推送天数
                                        attachments.setPushDayNumber(calculateActualDayNumber(baseTime,
                                                weSopPushTimeDto.getPushTimePre(),
                                                weSopPushTimeDto.getPushStartTime(),
                                                weSopPushTimeDto.getPushEndTime()));

                                        attachments.setPushStartTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushStartTime())
                                        );

                                        attachments.setPushEndTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushEndTime())
                                        );


                                    } else {
                                        //推送日期
                                        String pushDate = weSopPushTimeDto.getPushTimePre();

                                        attachments.setPushStartTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushStartTime())
                                        );

                                        attachments.setPushEndTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushEndTime())
                                        );
                                    }
                                } else if (weSopPushTimeDto.getPushTimeType() == 1) {//具体推送时间
                                    attachments.setPushStartTime(
                                            DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, weSopPushTimeDto.getPushTimePre() + " " + weSopPushTimeDto.getPushStartTime()));
                                    attachments.setPushEndTime(
                                            DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, weSopPushTimeDto.getPushTimePre() + " " + weSopPushTimeDto.getPushEndTime())
                                    );
                                }


                                if (weSopBase.getBusinessType().equals(SopType.SOP_TYPE_XK.getSopKey())) {
                                    if (buildXkSopPlan) {
                                        targetAttachments.add(attachments);
                                    }
                                } else {
                                    targetAttachments.add(attachments);
                                }


                            });


                        });

                        if (CollectionUtil.isNotEmpty(targetAttachments)) {

                            log.info("makeLabel-targetAttachments",JSONObject.toJSONString(targetAttachments));

                            iWeSopExecuteTargetAttachmentsService.saveOrUpdateBatch(targetAttachments);

                            Set<Long> sopExecuteTargetIds = weSopExecuteTargets.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toSet());

                            if (sopExecuteTargetIds.removeAll(
                                    targetAttachments.stream().map(WeSopExecuteTargetAttachments::getExecuteTargetId).collect(Collectors.toSet()))) {


                                executeTargetService.removeByIds(
                                        sopExecuteTargetIds
                                );


                            }

                        } else {//为空,则删除当前所有不满足条件的执行目标
                            executeTargetService.removeByIds(
                                    weSopExecuteTargets.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toSet())
                            );

                        }


                    }


                }


            }
        }
//        else{
//
//            //处理不满足当前条件的生效客群(针对客客户sop编辑)
//            executeTargetService.editSopExceptionEnd(weSopBase.getId(),
//                    new ArrayList<>());
//        }
    }

    @Override
    public void builderExecuteGroupSopPlan(WeSopBase weSopBase, Map<String, List<LinkGroupChatListVo>> executeGroups, boolean isCreateOrUpdate, boolean buildXkSopPlan) {
        if (CollectionUtil.isNotEmpty(executeGroups)) {


            List<WeSopExecuteTarget> weSopExecuteTargets = new ArrayList<>();
            executeGroups.forEach((k, v) -> {
                v.stream().forEach(group -> {
                    WeSopExecuteTarget weSopExecuteTarget = WeSopExecuteTarget.builder()
                            .targetId(group.getChatId())
                            .targetType(2)
                            .addCustomerOrCreateGoupTime(group.getAddTime())
                            .sopBaseId(weSopBase.getId())
                            .executeWeUserId(k)
                            .build();

                    weSopExecuteTargets.add(
                            weSopExecuteTarget
                    );
                });
            });


            if (CollectionUtil.isNotEmpty(weSopExecuteTargets)) {


                if (executeTargetService.saveOrUpdateBatch(weSopExecuteTargets)) {

                    List<WeSopPushTimeDto> weSopPushTimeDtos
                            = ((WeSopPushTimeMapper) iWeSopPushTimeService.getBaseMapper()).findWeSopPushTimeDto(weSopBase.getId());
                    if (CollectionUtil.isNotEmpty(weSopPushTimeDtos)) {

                        List<WeSopExecuteTargetAttachments> targetAttachments = new ArrayList<>();
                        weSopExecuteTargets.stream().forEach(weSopExecuteTarget -> {

                            weSopPushTimeDtos.stream().forEach(weSopPushTimeDto -> {

                                WeSopExecuteTargetAttachments attachments = WeSopExecuteTargetAttachments.builder()
                                        .executeTargetId(weSopExecuteTarget.getId())
                                        .sopAttachmentId(weSopPushTimeDto.getSopAttachmentId())
                                        .pushTimePre(weSopPushTimeDto.getPushTimePre())
                                        .pushTimeType(weSopPushTimeDto.getPushTimeType())
                                        .build();


                                if (weSopPushTimeDto.getPushTimeType() == 2) {//设置推送时间,周期推送，先计算出当前时间至周日下的推送,后续客每周日运行定时任务生成下一周的执行计划
                                    //获取当天是周几

                                    Integer weekDay = Integer.parseInt(weSopPushTimeDto.getPushTimePre());
                                    weekDay = weekDay % 7 + 1;

                                    //执行日期
                                    String executeData
                                            = WeekDateUtils.GetCurrentWeekAllDate().get(weekDay);

                                    if (StringUtils.isNotEmpty(executeData)) {
                                        Date pushEndTime
                                                = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, executeData + " " + weSopPushTimeDto.getPushEndTime());


                                        attachments.setPushStartTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, executeData + " " + weSopPushTimeDto.getPushStartTime())
                                        );

                                        attachments.setPushEndTime(
                                                pushEndTime
                                        );


                                    }


                                } else if (weSopPushTimeDto.getPushTimeType() == 1) {//具体推送时间
                                    attachments.setPushStartTime(
                                            DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, weSopPushTimeDto.getPushTimePre() + " " + weSopPushTimeDto.getPushStartTime()));
                                    attachments.setPushEndTime(
                                            DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, weSopPushTimeDto.getPushTimePre() + " " + weSopPushTimeDto.getPushEndTime())
                                    );
                                } else if (weSopPushTimeDto.getPushTimeType() == 3) { //新群

                                    if (weSopPushTimeDto.getPushTimePre().matches("^[0-9]*$")) {
                                        //推送日期 - 群SOP保持原有T+1逻辑
                                        String pushDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.daysAgoOrAfter(weSopExecuteTarget.getAddCustomerOrCreateGoupTime()
                                                , new Integer(weSopPushTimeDto.getPushTimePre())));

                                        // 设置群SOP的推送天数（保持原有逻辑，不使用T+0）
                                        attachments.setPushDayNumber(Integer.parseInt(weSopPushTimeDto.getPushTimePre()));

                                        attachments.setPushStartTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushStartTime())
                                        );

                                        attachments.setPushEndTime(
                                                DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, pushDate + " " + weSopPushTimeDto.getPushEndTime())
                                        );
                                    }


                                }


                                if (weSopBase.getBusinessType().equals(SopType.SOP_TYPE_XQPY.getSopKey())) {
                                    if (buildXkSopPlan) {
                                        if (attachments.getPushStartTime() != null && attachments.getPushEndTime() != null) {
                                            targetAttachments.add(attachments);
                                        }
                                    }
                                } else {
                                    if (attachments.getPushStartTime() != null && attachments.getPushEndTime() != null) {
                                        targetAttachments.add(attachments);
                                    }

                                }


                            });


                        });

                        if (CollectionUtil.isNotEmpty(targetAttachments)) {
                            iWeSopExecuteTargetAttachmentsService.saveOrUpdateBatch(targetAttachments);

                            Set<Long> sopExecuteTargetIds = weSopExecuteTargets.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toSet());

                            if (sopExecuteTargetIds.removeAll(
                                    targetAttachments.stream().map(WeSopExecuteTargetAttachments::getExecuteTargetId).collect(Collectors.toSet()))) {


                                executeTargetService.removeByIds(
                                        sopExecuteTargetIds
                                );


                            }

                        } else {//为空,则删除当前所有不满足条件的执行目标
                            executeTargetService.removeByIds(
                                    weSopExecuteTargets.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toSet())
                            );

                        }


                    }


                }


            }
        }

    }


    @Override
    public void synchSopExecuteResultForWeChatPushType(String sopBaseId) {

        List<WeSopExecuteTarget> weSopExecuteTargets = executeTargetService.list(new LambdaQueryWrapper<WeSopExecuteTarget>()
                .eq(WeSopExecuteTarget::getSopBaseId, sopBaseId)
                .eq(WeSopExecuteTarget::getDelFlag, Constants.COMMON_STATE)
                .eq(WeSopExecuteTarget::getExecuteState, SopExecuteStatus.SOP_STATUS_ING.getType()));
        if (CollectionUtil.isNotEmpty(weSopExecuteTargets)) {

            //获取具体执行计划
            List<WeSopExecuteTargetAttachments> targetAttachments = iWeSopExecuteTargetAttachmentsService.list(new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                    .eq(WeSopExecuteTargetAttachments::getDelFlag, Constants.COMMON_STATE)
                    .isNotNull(WeSopExecuteTargetAttachments::getMsgId)
                    .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
                    .in(WeSopExecuteTargetAttachments::getExecuteTargetId,
                            weSopExecuteTargets.stream().map(WeSopExecuteTarget::getId).collect(Collectors.toList())));

            if (CollectionUtil.isNotEmpty(targetAttachments)) {
                Map<Long, List<WeSopExecuteTargetAttachments>> targetAttachmentMap
                        = targetAttachments.stream().collect(Collectors.groupingBy(WeSopExecuteTargetAttachments::getExecuteTargetId));

                weSopExecuteTargets.stream().forEach(k -> {
                    List<WeSopExecuteTargetAttachments> sopExecuteTargetAttachment = targetAttachmentMap.get(k.getId());
                    if (CollectionUtil.isNotEmpty(sopExecuteTargetAttachment)) {

                        sopExecuteTargetAttachment.stream().forEach(kk -> {

                            WeGetGroupMsgListQuery listQuery = new WeGetGroupMsgListQuery();
                            listQuery.setMsgid(kk.getMsgId());
                            listQuery.setUserid(k.getExecuteWeUserId());

                            WeGroupMsgListVo groupMsgSendResult = qwCustomerClient.getGroupMsgSendResult(listQuery).getData();
                            Optional.ofNullable(groupMsgSendResult).map(WeGroupMsgListVo::getSendList).orElseGet(ArrayList::new).forEach(sendResult -> {
                                //设置发送时间
                                if (null != sendResult.getSendTime()) {
                                    kk.setExecuteTime(new Date(sendResult.getSendTime() * 1000L));
                                }
                                //设置任务执行状态
                                if (sendResult.getStatus() != 0) {
                                    kk.setExecuteState(1);
                                }
                            });
                        });

                        iWeSopExecuteTargetAttachmentsService.updateBatchById(sopExecuteTargetAttachment);


                        //校验如果全部任务都执行完，全局状态设置为完成
                        if (iWeSopExecuteTargetAttachmentsService.count(new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                .eq(WeSopExecuteTargetAttachments::getExecuteTargetId, k.getId())
                                .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
                                .eq(WeSopExecuteTargetAttachments::getDelFlag, Constants.COMMON_STATE)) == 0) {
                            k.setExecuteEndTime(new Date());
                            k.setExecuteState(SopExecuteStatus.SOP_STATUS_COMMON.getType());
                        }

                    }


                });

                executeTargetService.updateBatchById(weSopExecuteTargets);
            }


        }

    }


    @Override
    public void updateSopState(String sopId, Integer sopState) {
        this.baseMapper.updateSopState(sopId, sopState);
    }

    /**
     * 移除客户不再符合条件的SOP执行计划
     * 当客户标签变更后，检查现有执行计划是否仍然符合SOP条件
     */
    private void removeIneligibleSopExecutions(String userId, String externalUserid, WeCustomer customer) {
        try {
            // 获取客户当前参与的所有进行中的SOP执行计划
            List<WeSopExecuteTarget> currentExecuteTargets = iWeSopExecuteTargetService.list(
                new LambdaQueryWrapper<WeSopExecuteTarget>()
                    .eq(WeSopExecuteTarget::getTargetId, externalUserid)
                    .eq(WeSopExecuteTarget::getExecuteWeUserId, userId)
                    .eq(WeSopExecuteTarget::getTargetType, 1) // 客户类型
                    .eq(WeSopExecuteTarget::getExecuteState, SopExecuteStatus.SOP_STATUS_ING.getType())
            );

            if (CollectionUtil.isEmpty(currentExecuteTargets)) {
                return;
            }

            // 批量获取相关的SOP信息，避免N+1查询问题
            Set<Long> sopBaseIds = currentExecuteTargets.stream()
                    .map(WeSopExecuteTarget::getSopBaseId)
                    .collect(Collectors.toSet());
            Map<Long, WeSopBase> sopBaseMap = this.listByIds(sopBaseIds).stream()
                    .collect(Collectors.toMap(WeSopBase::getId, sop -> sop));

            int removedCount = 0;
            // 检查每个执行计划是否仍然符合SOP条件
            for (WeSopExecuteTarget executeTarget : currentExecuteTargets) {
                WeSopBase sopBase = sopBaseMap.get(executeTarget.getSopBaseId());

                // 如果SOP不存在或已停用，直接设置为异常结束
                if (sopBase == null || sopBase.getSopState() != 1) {
                    executeTarget.setExecuteState(SopExecuteStatus.SOP_STATUS_EXCEPTION.getType());
                    executeTarget.setExecuteEndTime(new Date());

                    if (iWeSopExecuteTargetService.updateById(executeTarget)) {
                        iWeSopExecuteTargetAttachmentsService.remove(
                            new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                .eq(WeSopExecuteTargetAttachments::getExecuteTargetId, executeTarget.getId())
                                .eq(WeSopExecuteTargetAttachments::getExecuteState, 0)
                        );
                        removedCount++;
                        log.info("makeLabel-客户因SOP不存在/停用从执行计划中移除，客户ID: {}, SOP ID: {}",
                                externalUserid, executeTarget.getSopBaseId());
                    }
                    continue;
                }

                // 检查客户是否仍符合SOP条件
                if (!checkCustomerEligibility(sopBase, customer)) {
                    // 客户不再符合SOP条件，设置为异常结束
                    executeTarget.setExecuteState(SopExecuteStatus.SOP_STATUS_EXCEPTION.getType());
                    executeTarget.setExecuteEndTime(new Date());

                    if (iWeSopExecuteTargetService.updateById(executeTarget)) {
                        // 删除未执行的相关执行内容记录
                        iWeSopExecuteTargetAttachmentsService.remove(
                            new LambdaQueryWrapper<WeSopExecuteTargetAttachments>()
                                .eq(WeSopExecuteTargetAttachments::getExecuteTargetId, executeTarget.getId())
                                .eq(WeSopExecuteTargetAttachments::getExecuteState, 0) // 未执行的
                        );

                        removedCount++;
                        log.info("makeLabel-客户因标签变更从SOP中移除，客户ID: {}, SOP ID: {}, SOP名称: {}",
                                externalUserid, sopBase.getId(), sopBase.getSopName());
                    } else {
                        log.warn("makeLabel-更新SOP执行状态失败，客户ID: {}, SOP ID: {}, 执行目标ID: {}",
                                externalUserid, sopBase.getId(), executeTarget.getId());
                    }
                }
            }

            if (removedCount > 0) {
                log.info("makeLabel-客户标签变更移除SOP执行计划完成，客户ID: {}, 员工ID: {}, 移除数量: {}",
                        externalUserid, userId, removedCount);
            }

        } catch (Exception e) {
            log.error("makeLabel-移除客户不符合条件的SOP执行计划失败，客户ID: {}, 员工ID: {}, 错误: {}",
                    externalUserid, userId, e.getMessage(), e);
        }
    }



}

