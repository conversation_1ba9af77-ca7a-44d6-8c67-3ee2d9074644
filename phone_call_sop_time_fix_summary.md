# 拨打电话SOP时间计算逻辑修复总结

## 问题描述

用户创建了一个拨打电话SOP，时间配置如下：
- 第1天 00:00-10:15
- 第1天 10:15-22:45  
- 第2天 00:00-03:15

但是在客户SOP详情列表-推送详情中，只显示了2个推送计划，而不是期望的3个。

## 问题分析

### 根本原因
拨打电话SOP与新客SOP在时间计算逻辑上存在差异：

1. **新客SOP**：使用客户添加时间作为基准时间
2. **拨打电话SOP**：使用当前时间作为基准时间

### 具体问题
当拨打电话SOP使用当前时间作为基准时间时，`calculateActualPushDate`方法中的时间窗口延迟逻辑会产生以下影响：

```java
// 原有逻辑（有问题）
Date baseTime = (weSopBase.getBusinessType() != null && weSopBase.getBusinessType().equals(7)) ?
                new Date() : weSopExecuteTarget.getAddCustomerOrCreateGoupTime();
```

如果在10:16创建拨打电话SOP：
- 第1天 00:00-10:15 时间段：由于当前时间(10:16)已过结束时间(10:15)，会被延迟到第2天
- 第1天 10:15-22:45 时间段：正常执行
- 第2天 00:00-03:15 时间段：正常执行

结果：原本3个推送计划变成了2个（第1个时间段被延迟合并）

## 修复方案

### 修改内容
将拨打电话SOP的基准时间计算逻辑修改为与新客SOP一致，统一使用客户添加时间：

**文件**: `api/scrm-module/scrm-business/src/main/java/org/scrm/service/impl/WeSopBaseServiceImpl.java`

**修改位置**: 第1802-1803行

**修改前**:
```java
// 确定计算基准时间：拨打电话SOP使用当前时间，其他SOP使用客户添加时间
Date baseTime = (weSopBase.getBusinessType() != null && weSopBase.getBusinessType().equals(7)) ?
                new Date() : weSopExecuteTarget.getAddCustomerOrCreateGoupTime();
```

**修改后**:
```java
// 确定计算基准时间：拨打电话SOP与新客SOP统一使用客户添加时间，保持时间计算逻辑一致
Date baseTime = weSopExecuteTarget.getAddCustomerOrCreateGoupTime();
```

### 注释更新
同时更新了相关方法的注释，明确说明拨打电话SOP也使用此逻辑：

**文件**: 同上
**修改位置**: 第670行
**修改内容**: 将注释中的"新客SOP、客户转化SOP"更新为"新客SOP、拨打电话SOP、客户转化SOP"

## 修复效果

修复后，拨打电话SOP将：

1. **时间计算一致性**：与新客SOP使用相同的时间计算逻辑
2. **推送计划完整性**：所有配置的时间段都会生成对应的推送计划
3. **延迟逻辑正确性**：基于客户添加时间而不是当前时间进行延迟判断

### 示例效果
对于配置：
- 第1天 00:00-10:15
- 第1天 10:15-22:45  
- 第2天 00:00-03:15

无论何时创建SOP，都会生成3个推送计划，确保时间配置的完整性。

## 代码质量保证

1. **最小化修改**：只修改了必要的逻辑，保持了代码的稳定性
2. **向后兼容**：不影响现有的新客SOP和其他SOP类型的功能
3. **逻辑统一**：消除了不同SOP类型间的时间计算差异
4. **注释更新**：保持了代码文档的准确性

## 测试建议

建议测试以下场景：
1. 创建拨打电话SOP，验证推送计划数量是否正确
2. 验证不同时间创建SOP，推送计划是否一致
3. 确认新客SOP功能未受影响
4. 验证时间窗口延迟逻辑在客户添加时间基础上正常工作
